using UnityEngine;
using Cinemachine;
using TTSDK;
using cfg;

public class DragMove : MonoBehaviour
{
    public CinemachineVirtualCamera vcam;
    public float moveSpeed = 0.1f; // 灵敏度
    public float zoomFOV = 30f;
    public float normalFOV = 60f;
    public float zoomSmoothSpeed = 10f;
    public bool isZooming;
    Vector3 lastMousePos;
    float currentFOV;
    bool isMobilePlatform = false;
    xy_limit xy_limit;

    void Start()
    {
        GameEventSystem.AddListener(GameEventType.StartAim, OnStartAim);
        GameEventSystem.AddListener(GameEventType.StopAim, OnStopAim);
        GameEventSystem.AddListener(GameEventType.ChangeScene, OnChangeScene);

        GetSystemInfo();
    }

    void OnStartAim()
    {
        isZooming = true;
    }

    void OnStopAim()
    {
        isZooming = false;
    }

    void OnChangeScene()
    {
        SceneLevel sceneLevel = GameEntry.Tables.TbSceneLevel[GameEntry.SaveData.currentLevelId];
        xy_limit = sceneLevel.CameraMoveLimit;
    }

    void GetSystemInfo()
    {
        if (CanIUse.GetSystemInfo)
        {
            var systemInfo = TT.GetSystemInfo();
            Debug.Log(JsonUtility.ToJson(systemInfo));

            if (systemInfo.platform == "ios" || systemInfo.platform == "android")
            {
                isMobilePlatform = true;
            }
        }
        else
        {
            Debug.Log("接口不兼容");
        }
    }

    void Update()
    {
        HandleTouchInput();
        HandleMouseInput();
        UpdateZoom();
    }

    void HandleMouseInput()
    {
        // 在移动平台上禁用鼠标输入
        if (isMobilePlatform) return;

        // 鼠标拖拽
        if (Input.GetMouseButtonDown(0))
        {
            lastMousePos = Input.mousePosition;
        }
        else if (Input.GetMouseButton(0))
        {
            Vector3 delta = Input.mousePosition - lastMousePos;
            lastMousePos = Input.mousePosition;

            // 转换为节点移动
            MoveByDelta(delta);
        }
    }

    void HandleTouchInput()
    {
        // 触摸滑动
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);

            if (touch.phase == TouchPhase.Moved)
            {
                Vector2 touchDelta = touch.deltaPosition;
                Vector2 delta = new(-touchDelta.x, -touchDelta.y);
                MoveByDelta(delta);
            }
        }
    }

    void UpdateZoom()
    {
        float targetFOV = isZooming ? zoomFOV : normalFOV;
        currentFOV = Mathf.Lerp(currentFOV, targetFOV, Time.deltaTime * zoomSmoothSpeed);
        vcam.m_Lens.FieldOfView = currentFOV;
    }

    void MoveByDelta(Vector2 delta)
    {
        // 注意这里除以屏幕尺寸 → 保证不同分辨率下相同体验
        float normalizedX = delta.x / Screen.width;
        float normalizedY = delta.y / Screen.height;

        Vector3 move = new Vector3(normalizedX, normalizedY, 0) * moveSpeed;
        transform.Translate(move, Space.World);

        // 限制范围
        if (xy_limit != null)
        {
            Vector3 pos = transform.position;
            pos.x = Mathf.Clamp(pos.x, xy_limit.Left, xy_limit.Right);
            pos.y = Mathf.Clamp(pos.y, xy_limit.Bottom, xy_limit.Top);
            transform.position = pos;
        }
    }
}
