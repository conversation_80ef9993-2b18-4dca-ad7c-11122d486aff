using UnityEngine;
using System.Collections;

public class ShootingSystem : MonoBehaviour
{
    [Header("Shooting Settings")]
    public float maxDistance = 100f;
    public int maxAmmo = 10;
    public float slowMotionTimeScale = 0.2f;
    public float offsetY = 144f; // Y轴偏移量，用于调整射线发射位置
    
    [Header("Effects")]
    public ParticleSystem muzzleFlash;
    public GameObject hitEffect;
    public GameObject targetHitEffect;
    
    private int currentAmmo;
    private bool isLastTarget;
    private Camera mainCamera;
    
    void Start()
    {
        currentAmmo = maxAmmo;
        mainCamera = Camera.main;
        Time.timeScale = 1f;
    }
    
    public void TryShoot()
    {
        if (currentAmmo <= 0) return;
        
        currentAmmo--;
        
        // 播放开火特效
        if (muzzleFlash != null)
            muzzleFlash.Play();
        
        // 发射射线
        Ray ray = mainCamera.ScreenPointToRay(new Vector3(Screen.width/2f, Screen.height/2f + offsetY, 0f));
        
        // 在编辑器中绘制射线
        #if UNITY_EDITOR
        Debug.DrawRay(ray.origin, ray.direction * maxDistance, Color.red, 2f);
        #endif
        
        if (Physics.Raycast(ray, out RaycastHit hit, maxDistance))
        {
            // 在编辑器中绘制命中点
            #if UNITY_EDITOR
            Debug.DrawLine(ray.origin, hit.point, Color.green, 2f);
            Debug.DrawRay(hit.point, hit.normal, Color.blue, 2f);
            #endif

            GameManager.Instance.CurBulletCount--;
            if (GameManager.Instance.CurBulletCount < 0)
            {
                GameManager.Instance.CurBulletCount = 0;
                return;
            }
            
            // 检查是否击中目标
            if (hit.collider.TryGetComponent<Target>(out var target))
            {
                // 播放目标击中特效
                if (targetHitEffect != null)
                {
                    GameObject effect = Instantiate(targetHitEffect, hit.point, Quaternion.FromToRotation(Vector3.up, hit.normal));
                    Destroy(effect, 2f);
                }

                // 触发目标被击中效果
                target.OnHit();

                // 如果是最后一个目标，启用慢动作
                if (isLastTarget)
                {
                    UIManager.Instance.HideUI<UIHUD>();
                    StartCoroutine(SlowMotionSequence(hit.point));
                }
            }
            else
            {
                GameEventSystem.Trigger(GameEventType.HitOther);

                // 播放普通击中特效
                if (hitEffect != null)
                {
                    GameObject effect = Instantiate(hitEffect, hit.point, Quaternion.FromToRotation(Vector3.up, hit.normal));
                    Destroy(effect, 2f);
                }
            }
        }
    }
    
    IEnumerator SlowMotionSequence(Vector3 hitPoint)
    {
        // 进入慢动作
        Time.timeScale = slowMotionTimeScale;
        Time.fixedDeltaTime = 0.02f * Time.timeScale;
        
        // 保存相机原始位置和旋转
        mainCamera.transform.GetPositionAndRotation(out Vector3 originalPosition, out Quaternion originalRotation);
        
        // 计算相机目标位置和旋转
        Vector3 direction = (hitPoint - originalPosition).normalized;
        Vector3 targetPosition = hitPoint - direction * 3f + Vector3.up * 1f;
        Quaternion targetRotation = Quaternion.LookRotation(direction);
        
        // 相机移动到目标位置
        float t = 0;
        while (t < 1)
        {
            t += Time.unscaledDeltaTime * 2f;
            mainCamera.transform.SetPositionAndRotation(
                Vector3.Lerp(originalPosition, targetPosition, t),
                Quaternion.Slerp(originalRotation, targetRotation, t));
            yield return null;
        }
        
        // 等待一段时间
        yield return new WaitForSecondsRealtime(1f);
        
        // 相机返回原位
        t = 0;
        while (t < 1)
        {
            t += Time.unscaledDeltaTime * 2f;
            mainCamera.transform.SetPositionAndRotation(
                Vector3.Lerp(targetPosition, originalPosition, t),
                Quaternion.Slerp(targetRotation, originalRotation, t));
            yield return null;
        }
        
        // 恢复正常速度
        Time.timeScale = 1f;
        Time.fixedDeltaTime = 0.02f;

        UIManager.Instance.ShowUI<UISettlement>(CanvasDefine.Normal, new SettlementParam(){ isWin = true });
    }
    
    public void SetLastTarget(bool isLast)
    {
        isLastTarget = isLast;
    }
    
    public int GetCurrentAmmo()
    {
        return currentAmmo;
    }
}