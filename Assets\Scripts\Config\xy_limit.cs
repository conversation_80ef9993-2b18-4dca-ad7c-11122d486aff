
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;
using SimpleJSON;


namespace cfg
{
public sealed partial class xy_limit : Luban.BeanBase
{
    public xy_limit(JSONNode _buf) 
    {
        { if(!_buf["left"].IsNumber) { throw new SerializationException(); }  Left = _buf["left"]; }
        { if(!_buf["right"].IsNumber) { throw new SerializationException(); }  Right = _buf["right"]; }
        { if(!_buf["bottom"].IsNumber) { throw new SerializationException(); }  Bottom = _buf["bottom"]; }
        { if(!_buf["top"].IsNumber) { throw new SerializationException(); }  Top = _buf["top"]; }
    }

    public static xy_limit Deserializexy_limit(JSONNode _buf)
    {
        return new xy_limit(_buf);
    }

    /// <summary>
    /// 左边界
    /// </summary>
    public readonly float Left;
    /// <summary>
    /// 右边界
    /// </summary>
    public readonly float Right;
    /// <summary>
    /// 下边界
    /// </summary>
    public readonly float Bottom;
    /// <summary>
    /// 上边界
    /// </summary>
    public readonly float Top;
   
    public const int __ID__ = 1520351485;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "left:" + Left + ","
        + "right:" + Right + ","
        + "bottom:" + Bottom + ","
        + "top:" + Top + ","
        + "}";
    }
}

}

