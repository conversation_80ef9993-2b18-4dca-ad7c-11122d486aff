using System;
using UnityEngine;

public class Target : MonoBehaviour
{
    [Header("Target Settings")]
    public float destroyDelay = 1f;
    public float scaleSpeed = 2f;
    public float rotateSpeed = 360f;

    public Transform parent;
    public AnimDefine animName = AnimDefine.站立;
    public Animator animator;
    public SkinnedMeshRenderer skinnedMeshRenderer;
    public MeshCollider meshCollider;
    
    private bool isHit = false;
    private Vector3 originalScale;
    
    void Start()
    {
        originalScale = transform.localScale;
        ChangeAnimState(animName);
    }

    void ChangeAnimState(AnimDefine animName)
    {
        if (animator == null) return;
        string stateName = Enum.GetName(typeof(AnimDefine), animName);
        animator.Play(stateName);
        AnimatorStateInfo animatorStateInfo = animator.GetCurrentAnimatorStateInfo(0);
        Invoke(nameof(RenderMesh), animatorStateInfo.length);
    }

    void RenderMesh()
    {
        if (skinnedMeshRenderer == null || meshCollider == null) return;
        Mesh temp = new()
        {
            name = "temp"
        };
        Vector3 parentScale = parent != null ? parent.localScale : Vector3.one;
        parent.localScale = Vector3.one;
        skinnedMeshRenderer.BakeMesh(temp);
        meshCollider.sharedMesh = temp;
        parent.localScale = parentScale;
    }

    public void OnHit()
    {
        if (isHit) return;
        isHit = true;

        GameManager.Instance.TargetNum--;

        // 通知射击系统这是否是最后一个目标
        int remainingTargets = FindObjectsOfType<Target>().Length - 1;
        ShootingSystem shootingSystem = FindObjectOfType<ShootingSystem>();
        if (shootingSystem != null)
        {
            shootingSystem.SetLastTarget(GameManager.Instance.TargetNum == 0);
        }

        GameManager.Instance.CheckCurFindEnemyDie(this);

        // 开始消失动画
        StartCoroutine(DestroySequence());
        
        GameEventSystem.Trigger(GameEventType.HitTarget);
    }
    
    System.Collections.IEnumerator DestroySequence()
    {
        float elapsed = 0f;
        
        while (elapsed < destroyDelay)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / destroyDelay;
            
            // 缩放效果
            transform.localScale = Vector3.Lerp(originalScale, Vector3.zero, t);
            
            // 旋转效果
            transform.Rotate(Vector3.up, rotateSpeed * Time.deltaTime);
            
            yield return null;
        }
        
        // 销毁目标
        Destroy(transform.parent.gameObject);
    }
}