%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1677285531, guid: 9bda8abe2c7386d4f93b1072454d336b, type: 3}
  m_Name: StarkBuilderSetting
  m_EditorClassIdentifier: 
  tag: 0
  wasmResourceUrl: http://
  webGLOutputDir: C:/Build/bytegame/CamoSniper/0.0.1
  useByteAudioAPI: 0
  wasmMemorySize: 128
  isWebGL2: 0
  needCompress: 1
  symbolMode: 1
  profiling: 0
  clearStreamingAssets: 0
  buildOptions: 0
  wasmSubFramework: 0
  optimizeWebGLMemoryInBackground: 1
  assetBundleFSEnabled: 1
  assetBundleBufferCapacity: 128
  assetBundleBufferTTL: 5
  preloadFiles: 
  CDN: 
  preloadDataListUrl: 
  iOSDevicePixelRatio: 0
  urlCacheList: []
  dontCacheFileNames: []
  isDevBuild: 0
  stripEngineCode: 0
  apkFileNameBase: 
  apkOutputDir: 
  appHost: 2
  compressMethod: 1
  runtimeEnv: 0
  framework: 1
  architecture: 2
  miniApkVersion: 0
  scriptingBackend: 0
  NativeWhiteListRegex:
  - com.bytedance.starksdk
  - _Backup~
  _appId: ttd099a6244f9520cf07
  version: 
  autoVersion: 1
  _webglPackagePath: C:\Build\bytegame\CamoSniper\0.0.1\webgl_package-20250905_214740.zip
  _orientation: 0
  _iOSPerformancePlus: 0
  _menuButtonStyle: 0
  _publishType: 2
  publishDesc: 
  idePath: "C:/Users/<USER>/AppData/Local/Programs/@bytedminiprogram-ide/\u6296\u97F3\u5F00\u53D1\u8005\u5DE5\u5177.exe"
