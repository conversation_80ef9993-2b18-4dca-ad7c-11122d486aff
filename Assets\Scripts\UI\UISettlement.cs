using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using cfg;

public partial class UISettlement : UIPanel
{
    public float rotateSpeed = 10f;

    protected override void Awake()
    {
        base.Awake();

        InitBind();

        ImgLight.transform.DOLocalRotate(new Vector3(0, 0, -360f), rotateSpeed, RotateMode.FastBeyond360).SetEase(Ease.Linear).SetLoops(-1);
    }

    public override void OnShow(object param = null)
    {
        base.OnShow();

        if (param is SettlementParam args)
        {
            GoWin.SetActive(args.isWin);
            GoFail.SetActive(!args.isWin);

            GoWinNew.SetActive(args.isNew);
            GoWinNext.SetActive(!args.isNew);

            TxtWinNew.gameObject.SetActive(args.isNew);
            TxtWinNext.gameObject.SetActive(!args.isNew);

            if (args.isWin)
            {
                SDKManager.Instance.UploadRankData(GameEntry.SaveData.currentLevelId);
            }
        }
        RefreshPanel();
        GameEventSystem.AddListener<string>(GameEventType.EnergyCountDown, OnEnergyCountDown);
    }

    public override void OnHide()
    {
        base.OnHide();
        GameEventSystem.RemoveListener<string>(GameEventType.EnergyCountDown, OnEnergyCountDown);
    }

    void OnEnergyCountDown(string timeStr)
    {
        TxtTime.text = timeStr;
        TxtEnergy.text = GameEntry.SaveData.playerData.energy.ToString();
    }

    void OnBtnAddClick()
    {
        UIManager.Instance.ShowUI<UIEnergy>(CanvasDefine.Top);
    }

    void OnBtnRecordClick()
    {
        FlyManager.Instance.FlyTextMiddle(Vector2.zero, "功能暂未实现");
    }

    void OnBtnHomeClick()
    {
        UIManager.Instance.HideUI<UISettlement>();
        UIManager.Instance.ShowUI<UIMainFace>();

        GameEntry.Resource.UnloadCurrentLevelScene();
    }

    void OnBtnShareClick()
    {
        FlyManager.Instance.FlyTextMiddle(Vector2.zero, "功能暂未实现");
    }

    void OnBtnNextClick()
    {
        int nextLevelId = GameEntry.SaveData.currentLevelId + 1;
        if (GameEntry.Tables.TbSceneLevel.DataMap.ContainsKey(nextLevelId))
        {
            GameEntry.SaveData.currentLevelId = nextLevelId;
            SaveManager.Instance.Save();
            SceneLevel sceneLevel = GameEntry.Tables.TbSceneLevel[GameEntry.SaveData.currentLevelId];
            ResourceManager.Instance.LoadSceneAdditive(sceneLevel.ScenePath, (scene) =>
            {
                GameManager.Instance.TargetNum = 3;
                GameManager.Instance.CurBulletCountMax = sceneLevel.BulletCount;
                GameManager.Instance.CurBulletCount = sceneLevel.BulletCount;
            });
            UIManager.Instance.HideUI<UISettlement>();
            UIManager.Instance.ShowUI<UIHUD>();
        }
    }

    void OnBtnNewClick()
    {

    }

    void OnBtnBackClick()
    {
        UIManager.Instance.HideUI<UISettlement>();
        UIManager.Instance.ShowUI<UIMainFace>();

        GameEntry.Resource.UnloadCurrentLevelScene();
    }

    void OnBtnReliveClick()
    {
        SDKManager.Instance.ShowRewardAd(()=>
        {
            SceneLevel sceneLevel = GameEntry.Tables.TbSceneLevel[GameEntry.SaveData.currentLevelId];
            int time = sceneLevel.Time;
            GameManager.Instance.CurTime = time;

            UIManager.Instance.HideUI<UISettlement>();
            UIManager.Instance.ShowUI<UIHUD>();
        });
    }

    void OnBtnRestartClick()
    {
        if (GameEntry.SaveData.playerData.energy <= 0)
        {
            FlyManager.Instance.FlyTextMiddle(Vector2.zero, "体力不足");
            return;
        }
        SceneLevel sceneLevel = GameEntry.Tables.TbSceneLevel[GameEntry.SaveData.currentLevelId];
        int time = sceneLevel.Time;
        GameManager.Instance.CurTime = time;
        GameManager.Instance.CurBulletCountMax = sceneLevel.BulletCount;
        GameManager.Instance.CurBulletCount = sceneLevel.BulletCount;

        int energyMax = GameEntry.Tables.TbGlobalSetting[1].EnergyMax;
        if (GameEntry.SaveData.playerData.energy >= energyMax)
        {
            GameEntry.SaveData.playerData.energyRefreshTime = DateTime.Now;
        }
        GameEntry.SaveData.playerData.energy--;

        UIManager.Instance.HideUI<UISettlement>();
        UIManager.Instance.ShowUI<UIHUD>();

        ResourceManager.Instance.LoadSceneAdditive(sceneLevel.ScenePath, (scene) =>
        {
            GameManager.Instance.TargetNum = 3;
        });

        SaveManager.Instance.Save();
    }

    void RefreshPanel()
    {
        TxtEnergy.text = GameEntry.SaveData.playerData.energy.ToString();
        TxtTime.text = GameManager.Instance.EnergyTimerStr;

        int remainingTargets = GameManager.Instance.TargetNum;
        TxtRemainEnemy.text = remainingTargets.ToString();
    }
}
