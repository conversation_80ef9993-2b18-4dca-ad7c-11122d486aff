using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using TTSDK;
using TTSDK.UNBridgeLib.LitJson;
using Newtonsoft.Json;

public class SDKManager : Singleton<SDKManager>
{
    public void ShowRewardAd(Action callback)
    {
        UIManager.Instance.ShowUI<UIAdSimulate>(CanvasDefine.Top, new AdParam() { callback = callback });
    }

    public void UploadRankData(int levelId)
    {
#if !UNITY_EDITOR
        var paramJson = new JsonData()
        {
            ["dataType"] = 0,
            ["value"] = levelId.ToString(),
        };
        Debug.Log($"SetImRankData param:{paramJson.ToJson()}");
        TT.SetImRankData(paramJson, (isSuccess, errMsg) =>
        {
            if (isSuccess)
            {
                Debug.Log($"排行榜上传成功 {errMsg}");
            }
            else
            {
                Debug.Log($"排行榜上传失败 {errMsg}");
            }
        });
#endif
    }

    public void ShowRank()
    {
#if UNITY_EDITOR
        FlyManager.Instance.FlyTextMiddle(Vector2.zero, "不支持编辑器环境下查看");
#else
        TT.CheckSession(() =>
        {
            Debug.Log("已登录");
            var paramJson = new JsonData
            {
                ["rankType"] = "all",
                ["dataType"] = 0,
                ["relationType"] = "default",
                ["suffix"] = "关",
                ["rankTitle"] = "排行榜",
            };
            Debug.Log($"GetImRankList param:{paramJson.ToJson()}");
            TT.GetImRankList(paramJson, (isSuccess, errMsg) =>
            {
                if (isSuccess)
                {
                    Debug.Log($"排行榜显示成功 {errMsg}");
                }
                else
                {
                    Debug.Log($"排行榜显示失败 {errMsg}");
                }
            });
        }, (errMsg) =>
        {
            Debug.Log($"未登录 {errMsg}");
            TT.Login((code, anonymousCode, isLogin) =>
            {
                Debug.Log($"登录成功 code = {code} anonymousCode = {anonymousCode} isLogin = {isLogin}");
                if (isLogin)
                {
                    var paramJson = new JsonData
                    {
                        ["rankType"] = "all",
                        ["dataType"] = 0,
                        ["relationType"] = "default",
                        ["suffix"] = "关",
                        ["rankTitle"] = "排行榜",
                    };
                    Debug.Log($"GetImRankList param:{paramJson.ToJson()}");
                    TT.GetImRankList(paramJson, (isSuccess, errMsg) =>
                    {
                        if (isSuccess)
                        {
                            Debug.Log($"排行榜显示成功 {errMsg}");
                        }
                        else
                        {
                            Debug.Log($"排行榜显示失败 {errMsg}");
                        }
                    });
                }
            }, (errMsg)=>
            {
                Debug.Log($"登录失败 {errMsg}");
            });
        });
#endif
    }

    public void Share()
    {
#if UNITY_EDITOR
        FlyManager.Instance.FlyTextMiddle(Vector2.zero, "不支持编辑器环境下查看");
#else
        // 通用分享
        JsonData shareJson = new()
        {
            ["title"] = "考考你的眼力",
            ["desc"] = "你能找到隐藏在场景中的所有目标吗？",
        };
        Debug.Log($"ShareAppMessageBtnClicked jsonData: {shareJson.ToJson()}");
        TT.ShareAppMessage(shareJson,(data) =>
        {
            Debug.Log($"ShareAppMessage success: {JsonConvert.SerializeObject(data)}");
        },
        (errMsg) =>
        {
            Debug.Log($"ShareAppMessage failed: {errMsg}");
        },
        () =>
        {
            Debug.Log($"ShareAppMessage cancel");
        });
#endif
    }

    public void CheckSideBar()
    {
        TT.CheckScene(TTSideBar.SceneEnum.SideBar, b =>
        {
            Debug.Log("check scene success, " + b);
            TT.GetAppLifeCycle().OnShow += OnAppShow;
        }, () =>
        {
            Debug.Log("check scene complete");
        }, (errCode, errMsg) =>
        {
            Debug.Log($"check scene error, errCode:{errCode}, errMsg:{errMsg}");
        });
    }

    private void OnAppShow(Dictionary<string, object> param)
    {
        Debug.Log("OnAppShow");
        string json = JsonConvert.SerializeObject(param);
        Debug.Log($"显示回调参数 json = {json}");

        string scene = string.Empty;
        string launchFrom = string.Empty;
        string location = string.Empty;

        if (param.ContainsKey("scene")) scene = param["scene"].ToString();
        if (param.ContainsKey("launchFrom")) launchFrom = param["launchFrom"].ToString();
        if (param.ContainsKey("location")) location = param["location"].ToString();

        Debug.Log($"scene = {scene} launchFrom = {launchFrom} location = {location}");
        
        if ("021036".Equals(scene) && "homepage".Equals(launchFrom) && "sidebar_card".Equals(location))
        {
            Debug.Log("刷新侧边栏引导界面");
        }
    }

    public void NavigateToSideBar()
    {
        var data = new JsonData
        {
            ["scene"] = "sidebar",
        };
        TT.NavigateToScene(data, () =>
        {
            Debug.Log("navigate to scene success");
        }, () =>
        {
            Debug.Log("navigate to scene complete");
        }, (errCode, errMsg) =>
        {
            Debug.Log($"navigate to scene error, errCode:{errCode}, errMsg:{errMsg}");
        });
    }

    public void AddShortCut()
    {
#if UNITY_EDITOR
        FlyManager.Instance.FlyTextMiddle(Vector2.zero, "不支持编辑器环境下查看");
#else
        TT.AddShortcut((result) =>
        {
            Debug.Log($"AddShortcut result: {result}");
        });
#endif
    }
}
