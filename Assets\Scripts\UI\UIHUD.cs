using System.Collections;
using System.Collections.Generic;
using TTSDK;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public partial class UIHUD : UIPanel
{
    private Vector2 touchStartPos;
    public float CLICK_THRESHOLD = 10f; // 点击判定阈值
    bool isMobilePlatform = false;
    bool isMoving = false;

    readonly Dictionary<int, string> spriteDict = new()
    {
        [0] = "Assets/ResPackage/Images/Gameplay/ui_gameplay_normalenemy_kill.png",
        [1] = "Assets/ResPackage/Images/Gameplay/ui_gameplay_normalenemy_alive.png",
    };

    protected override void Awake()
    {
        base.Awake();
        InitBind();

        GoShoot.SetActive(false);

        GetSystemInfo();

        SafeArea safeArea = GameManager.Instance.SafeArea;
        RectCircle.anchoredPosition = new Vector2(0, RectCircle.anchoredPosition.y + safeArea.top / 2);

        RectTransform rectSight = GoSight.GetComponent<RectTransform>();
        rectSight.anchoredPosition = new Vector2(0, rectSight.anchoredPosition.y + safeArea.top / 2);
    }

    public override void OnShow(object param = null)
    {
        base.OnShow();
        GameEventSystem.AddListener(GameEventType.HitTarget, OnHitTarget);
        GameEventSystem.AddListener(GameEventType.HitOther, OnHitOther);
        GameEventSystem.AddListener(GameEventType.ChangeScene, OnChangeScene);

        StartTimer();
        RefreshEnemyIcon();
        RefreshPanel();
    }

    public override void OnHide()
    {
        base.OnHide();
        GameEventSystem.RemoveListener(GameEventType.HitTarget, OnHitTarget);
        GameEventSystem.RemoveListener(GameEventType.HitOther, OnHitOther);
        GameEventSystem.RemoveListener(GameEventType.ChangeScene, OnChangeScene);

        StopTimer();
        ResetPanel();
    }
    
    void GetSystemInfo()
    {
        if (CanIUse.GetSystemInfo)
        {
            var systemInfo = TT.GetSystemInfo();
            Debug.Log(JsonUtility.ToJson(systemInfo));

            if (systemInfo.platform == "ios" || systemInfo.platform == "android")
            {
                isMobilePlatform = true;
            }
        }
        else
        {
            Debug.Log("接口不兼容");
        }
    }

    void Update()
    {
        if (isMobilePlatform)
        {
            // 检测触摸输入
            if (Input.touchCount > 0)
            {
                Touch touch = Input.GetTouch(0);
                
                if (touch.phase == TouchPhase.Began)
                {
                    touchStartPos = touch.position;
                    isMoving = false;
                }
                else if (touch.phase == TouchPhase.Ended)
                {
                    float distance = Vector2.Distance(touchStartPos, touch.position);
                    if (distance > CLICK_THRESHOLD)
                    {
                        isMoving = true;
                    }
                }
            }
        }
        else
        {
            // 检测鼠标输入
            if (Input.GetMouseButtonDown(0))
            {
                touchStartPos = Input.mousePosition;
                isMoving = false;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                float distance = Vector2.Distance(touchStartPos, Input.mousePosition);
                if (distance > CLICK_THRESHOLD)
                {
                    isMoving = true;
                }
            }
        }
    }

    void OnBtnStartAimClick()
    {
        GameEventSystem.Trigger(GameEventType.StartAim);
        BtnStartAim.gameObject.SetActive(false);
        GoShoot.SetActive(true);
        GoSight.SetActive(false);
        GoGroup.SetActive(false);
    }

    void OnBtnStopAimClick()
    {
        Update();
        if (isMoving) return;
        GameEventSystem.Trigger(GameEventType.StopAim);
        BtnStartAim.gameObject.SetActive(true);
        GoShoot.SetActive(false);
        GoSight.SetActive(true);
        GoGroup.SetActive(true);
    }

    void OnBtnSettingClick()
    {
        UIManager.Instance.ShowUI<UISetting>(CanvasDefine.Top, new SettingParam(){ isInGame = true });
    }

    void OnBtnAddTimeClick()
    {
        SDKManager.Instance.ShowRewardAd(()=>
        {
            GameManager.Instance.CurTime += 60;
            RefreshPanel();
        });
    }

    void OnBtnSearchClick()
    {
        SDKManager.Instance.ShowRewardAd(()=>
        {
            GameManager.Instance.FindEnemy();
        });
    }

    void OnBtnAddAmmoClick()
    {
        SDKManager.Instance.ShowRewardAd(()=>
        {
            GameManager.Instance.CurBulletCount += 10;
            RefreshPanel();
        });
    }

    void OnBtnExterminateClick()
    {

    }

    void OnBtnShootClick()
    {
        // 通知射击系统
        ShootingSystem shootingSystem = FindObjectOfType<ShootingSystem>();
        if (shootingSystem != null)
        {
            shootingSystem.TryShoot();
        }
    }

    void OnHitTarget()
    {
        RefreshEnemyIcon();
        RefreshPanel();
    }

    void OnHitOther()
    {
        RefreshPanel();
    }

    void OnChangeScene()
    {
        for (int i = 0; i < GoTargetNum.transform.childCount; i++)
        {
            Image image = GoTargetNum.transform.GetChild(i).GetComponent<Image>();
            GameEntry.Resource.LoadImage(spriteDict[1], (result)=>
            {
                image.sprite = result;
            });
        }
    }

    void ResetPanel()
    {
        GameEventSystem.Trigger(GameEventType.StopAim);
        BtnStartAim.gameObject.SetActive(true);
        GoShoot.SetActive(false);
        GoSight.SetActive(true);
        GoGroup.SetActive(true);

        OnChangeScene();
    }

    void RefreshPanel()
    {
        int curLevelID = GameEntry.SaveData.currentLevelId;
        TxtLevel.text = $"第{curLevelID}关";
        int time = GameManager.Instance.CurTime;
        TxtTime.text = string.Format("{0:00}:{1:00}", time / 60, time % 60);

        int curBulletCountMax = GameManager.Instance.CurBulletCountMax;
        if (curBulletCountMax == 0)
        {
            TxtAmmo.text = "无限";
        }
        else
        {
            int curBulletCount = GameManager.Instance.CurBulletCount;
            TxtAmmo.text = curBulletCount.ToString();
        }
    }

    void RefreshEnemyIcon()
    {
        int remainingTargets = GameManager.Instance.TargetNum;
        for (int i = 0; i < GoTargetNum.transform.childCount; i++)
        {
            if (i < remainingTargets)
            {
                Image image = GoTargetNum.transform.GetChild(i).GetComponent<Image>();
                GameEntry.Resource.LoadImage(spriteDict[1], (result)=>
                {
                    image.sprite = result;
                });
            }
            else
            {
                Image image = GoTargetNum.transform.GetChild(i).GetComponent<Image>();
                GameEntry.Resource.LoadImage(spriteDict[0], (result)=>
                {
                    image.sprite = result;
                });
            }
        }
    }

    void StartTimer()
    {
        int curLevelID = GameEntry.SaveData.currentLevelId;
        int time = GameManager.Instance.CurTime;
        TxtTime.text = string.Format("{0:00}:{1:00}", time / 60, time % 60);

        Timer timer = TimerManager.Instance.CreateTimer("UIHUD", 1f, -1);
        timer.SetOnComplete(()=>
        {
            GameManager.Instance.CurTime--;
            time = GameManager.Instance.CurTime;

            if (time < 0)
            {
                time = 0;
                StopTimer();

                UIManager.Instance.HideUI<UIHUD>();
                UIManager.Instance.ShowUI<UISettlement>(CanvasDefine.Normal, new SettlementParam(){ isWin = false });
            }

            TxtTime.text = string.Format("{0:00}:{1:00}", time / 60, time % 60);
        });
    }

    void StopTimer()
    {
        TimerManager.Instance.StopTimer("UIHUD");
    }
}
